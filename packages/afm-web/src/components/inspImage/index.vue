<template>
  <div class="insp-image-container">
    <div class="insp-image-toolbar">
      <div v-for="tool in drawTools" :key="tool.type" class="tool-item" :class="{ active: currentTool === tool.type }"
        @click="selectTool(tool.type)">
        {{ tool.label }}
      </div>
    </div>
    <div class="insp-image-wrapper">
      <div class="insp-image-wrapper-top">
        <div class="insp-image-wrapper-top-left">
          <div class="insp-image-content" ref="imageContainer">
            <img v-if="src" :src="src" ref="imageEl" @load="handleImageLoad" class="image" />
            <canvas ref="canvas" class="drawing-canvas" @mousedown="handleMouseDown" @mousemove="handleMouseMove"
              @mouseup="handleMouseUp" @mouseleave="handleMouseLeave"></canvas>
          </div>
        </div>
        <div class="insp-image-wrapper-top-right">
          <div class="color-bar"></div>
        </div>
      </div>
      <div class="insp-image-wrapper-bottom">
        <div class="insp-image-wrapper-bottom-top">
          <div>0μm</div>
          <div>20μm</div>
        </div>
        <div class="insp-image-wrapper-bottom-bottom">
          <el-button size="small" @click="handleOriginal">原始数据</el-button>
          <el-button size="small" @click="handleDeal">处理后的数据</el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'

const props = defineProps({
  src: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['mark'])

// 绘图工具
const drawTools = ref([
  { type: 'line', label: '直线' },
  { type: 'circle', label: '圆形' },
  { type: 'rect', label: '矩形' },
  { type: 'polygon', label: '多边形' },
  { type: 'measure', label: '测量' },
  { type: 'scale', label: '放大镜' },
  { type: 'reset', label: '还原' },
  { type: 'erase', label: '擦除' }
])

// 当前选中的工具
const currentTool = ref('')

// DOM引用
const imageContainer = ref<HTMLDivElement | null>(null)
const imageEl = ref<HTMLImageElement | null>(null)
const canvas = ref<HTMLCanvasElement | null>(null)

// 图片原始尺寸
const imageNaturalWidth = ref(0)
const imageNaturalHeight = ref(0)

// 图片显示尺寸
const imageDisplayWidth = ref(0)
const imageDisplayHeight = ref(0)

// 绘图状态
const isDrawing = ref(false)
const startPoint = ref({ x: 0, y: 0 })
const currentPoint = ref({ x: 0, y: 0 })

// 多边形绘制点
const polygonPoints = ref<{ x: number, y: number }[]>([])
const isDrawingPolygon = ref(false)

// 清除画布
const clearCanvas = () => {
  if (!canvas.value) return

  const ctx = canvas.value.getContext('2d')
  if (!ctx) return

  ctx.clearRect(0, 0, canvas.value.width, canvas.value.height)
}

// 选择工具
const selectTool = (tool: string) => {
  currentTool.value = tool
  clearCanvas()
  isDrawing.value = false
  polygonPoints.value = []
  isDrawingPolygon.value = false
}

// 处理图片加载
const handleImageLoad = () => {
  if (!imageEl.value || !canvas.value || !imageContainer.value) return

  imageNaturalWidth.value = imageEl.value.naturalWidth
  imageNaturalHeight.value = imageEl.value.naturalHeight

  // 设置Canvas尺寸为容器尺寸
  const containerWidth = imageContainer.value.clientWidth
  const containerHeight = imageContainer.value.clientHeight

  // 计算图片的显示尺寸，保持宽高比
  const ratio = Math.min(
    containerWidth / imageNaturalWidth.value,
    containerHeight / imageNaturalHeight.value
  )

  imageDisplayWidth.value = imageNaturalWidth.value * ratio
  imageDisplayHeight.value = imageNaturalHeight.value * ratio

  // 设置Canvas尺寸
  canvas.value.width = containerWidth
  canvas.value.height = containerHeight

  // 设置图片样式
  imageEl.value.style.width = `${imageDisplayWidth.value}px`
  imageEl.value.style.height = `${imageDisplayHeight.value}px`
}

// 转换Canvas坐标到图片实际坐标
const convertToImageCoordinates = (x: number, y: number) => {
  if (!imageEl.value || !canvas.value) return { x: 0, y: 0 }

  // 获取Canvas在页面中的位置
  const rect = canvas.value.getBoundingClientRect()

  // 计算鼠标在Canvas中的相对位置
  const canvasX = x - rect.left
  const canvasY = y - rect.top

  // 计算图片在Canvas中的位置（居中显示）
  const imageLeft = (canvas.value.width - imageDisplayWidth.value) / 2
  const imageTop = (canvas.value.height - imageDisplayHeight.value) / 2

  // 计算鼠标相对于图片的位置
  const imageX = canvasX - imageLeft
  const imageY = canvasY - imageTop

  // 转换为原始图片坐标系下的坐标
  const originalX = Math.round((imageX / imageDisplayWidth.value) * imageNaturalWidth.value)
  const originalY = Math.round((imageY / imageDisplayHeight.value) * imageNaturalHeight.value)

  // 确保坐标在图片范围内
  const boundedX = Math.max(0, Math.min(imageNaturalWidth.value, originalX))
  const boundedY = Math.max(0, Math.min(imageNaturalHeight.value, originalY))

  return { x: boundedX, y: boundedY }
}

// 检查点是否在图片区域内
const isPointInImage = (x: number, y: number) => {
  if (!imageEl.value || !canvas.value) return false

  const rect = canvas.value.getBoundingClientRect()
  const canvasX = x - rect.left
  const canvasY = y - rect.top

  const imageLeft = (canvas.value.width - imageDisplayWidth.value) / 2
  const imageTop = (canvas.value.height - imageDisplayHeight.value) / 2

  return (
    canvasX >= imageLeft &&
    canvasX <= imageLeft + imageDisplayWidth.value &&
    canvasY >= imageTop &&
    canvasY <= imageTop + imageDisplayHeight.value
  )
}

// 绘制临时图形
const drawTemporaryShape = () => {
  if (!canvas.value || !currentTool.value) return

  const ctx = canvas.value.getContext('2d')
  if (!ctx) return

  // 清除当前画布
  clearCanvas()

  // 获取图片位置
  const imageLeft = (canvas.value.width - imageDisplayWidth.value) / 2
  const imageTop = (canvas.value.height - imageDisplayHeight.value) / 2

  // 设置绘图样式
  ctx.strokeStyle = '#125140'
  ctx.lineWidth = 2
  ctx.setLineDash([])

  const scale = {
    x: imageDisplayWidth.value / imageNaturalWidth.value,
    y: imageDisplayHeight.value / imageNaturalHeight.value
  }

  // 根据当前工具绘制不同图形
  switch (currentTool.value) {
    case 'line':
      ctx.beginPath()
      ctx.moveTo(
        imageLeft + startPoint.value.x * scale.x,
        imageTop + startPoint.value.y * scale.y
      )
      ctx.lineTo(
        imageLeft + currentPoint.value.x * scale.x,
        imageTop + currentPoint.value.y * scale.y
      )
      ctx.stroke()
      break

    case 'circle':
      const radius = Math.sqrt(
        Math.pow(currentPoint.value.x - startPoint.value.x, 2) +
        Math.pow(currentPoint.value.y - startPoint.value.y, 2)
      )
      ctx.beginPath()
      ctx.arc(
        imageLeft + startPoint.value.x * scale.x,
        imageTop + startPoint.value.y * scale.y,
        radius * scale.x,
        0,
        2 * Math.PI
      )
      ctx.stroke()
      break

    case 'rect':
      const width = currentPoint.value.x - startPoint.value.x
      const height = currentPoint.value.y - startPoint.value.y
      ctx.strokeRect(
        imageLeft + startPoint.value.x * scale.x,
        imageTop + startPoint.value.y * scale.y,
        width * scale.x,
        height * scale.y
      )
      break

    case 'polygon':
      if (polygonPoints.value.length > 0) {
        ctx.beginPath()
        ctx.moveTo(
          imageLeft + polygonPoints.value[0].x * scale.x,
          imageTop + polygonPoints.value[0].y * scale.y
        )

        for (let i = 1; i < polygonPoints.value.length; i++) {
          ctx.lineTo(
            imageLeft + polygonPoints.value[i].x * scale.x,
            imageTop + polygonPoints.value[i].y * scale.y
          )
        }

        // 连接到当前点
        if (isDrawingPolygon.value) {
          ctx.lineTo(
            imageLeft + currentPoint.value.x * scale.x,
            imageTop + currentPoint.value.y * scale.y
          )
        } else {
          // 闭合多边形
          ctx.lineTo(
            imageLeft + polygonPoints.value[0].x * scale.x,
            imageTop + polygonPoints.value[0].y * scale.y
          )
        }

        ctx.stroke()
      }
      break
  }
}

// 鼠标按下事件
const handleMouseDown = (e: MouseEvent) => {
  if (!currentTool.value || !isPointInImage(e.clientX, e.clientY)) return

  const point = convertToImageCoordinates(e.clientX, e.clientY)

  if (currentTool.value === 'polygon') {
    // 多边形处理逻辑
    if (!isDrawingPolygon.value) {
      // 开始绘制多边形
      isDrawingPolygon.value = true
      polygonPoints.value = [point]
    } else {
      // 判断是否点击接近起点，如果是则完成多边形
      const firstPoint = polygonPoints.value[0]
      const distance = Math.sqrt(
        Math.pow(point.x - firstPoint.x, 2) +
        Math.pow(point.y - firstPoint.y, 2)
      )

      if (distance < 10 && polygonPoints.value.length > 2) {
        // 完成多边形
        finishDrawing()
      } else {
        // 添加新点
        polygonPoints.value.push(point)
        currentPoint.value = point
        drawTemporaryShape()
      }
    }
  } else {
    // 其他形状处理逻辑
    isDrawing.value = true
    startPoint.value = point
    currentPoint.value = point
  }
}

// 鼠标移动事件
const handleMouseMove = (e: MouseEvent) => {
  if (
    (!isDrawing.value && !isDrawingPolygon.value) ||
    !currentTool.value ||
    !isPointInImage(e.clientX, e.clientY)
  ) return

  currentPoint.value = convertToImageCoordinates(e.clientX, e.clientY)
  drawTemporaryShape()
}

// 鼠标抬起事件
const handleMouseUp = (e: MouseEvent) => {
  if (!isDrawing.value || !currentTool.value || currentTool.value === 'polygon') return

  currentPoint.value = convertToImageCoordinates(e.clientX, e.clientY)
  finishDrawing()
}

// 鼠标离开事件
const handleMouseLeave = (e: MouseEvent) => {
  if (!isDrawing.value || !currentTool.value || currentTool.value === 'polygon') return

  currentPoint.value = convertToImageCoordinates(e.clientX, e.clientY)
  finishDrawing()
}

// 完成绘制并发送数据
const finishDrawing = () => {
  if (!currentTool.value) return

  let markData: any = null

  switch (currentTool.value) {
    case 'line':
      markData = {
        type: 'line',
        data: [
          { x: startPoint.value.x, y: startPoint.value.y },
          { x: currentPoint.value.x, y: currentPoint.value.y }
        ]
      }
      break

    case 'circle':
      const radius = Math.sqrt(
        Math.pow(currentPoint.value.x - startPoint.value.x, 2) +
        Math.pow(currentPoint.value.y - startPoint.value.y, 2)
      )
      markData = {
        type: 'circle',
        data: {
          x: startPoint.value.x,
          y: startPoint.value.y,
          radius: Math.round(radius)
        }
      }
      break

    case 'rect':
      const x = Math.min(startPoint.value.x, currentPoint.value.x)
      const y = Math.min(startPoint.value.y, currentPoint.value.y)
      const w = Math.abs(currentPoint.value.x - startPoint.value.x)
      const h = Math.abs(currentPoint.value.y - startPoint.value.y)
      markData = {
        type: 'rect',
        data: { x, y, w, h }
      }
      break

    case 'polygon':
      markData = {
        type: 'polygon',
        data: [...polygonPoints.value]
      }
      break
  }

  if (markData) {
    emit('mark', markData)
  }

  // 重置绘图状态
  isDrawing.value = false
  isDrawingPolygon.value = false
}

// 根据传入的标记数据绘制图形
const setMark = (markData: any) => {
  if (!canvas.value || !markData || !markData.type) return

  const ctx = canvas.value.getContext('2d')
  if (!ctx) return

  // 清除当前画布
  clearCanvas()

  // 获取图片位置
  const imageLeft = (canvas.value.width - imageDisplayWidth.value) / 2
  const imageTop = (canvas.value.height - imageDisplayHeight.value) / 2

  // 设置绘图样式
  ctx.strokeStyle = '#125140'
  ctx.lineWidth = 2
  ctx.setLineDash([])

  const scale = {
    x: imageDisplayWidth.value / imageNaturalWidth.value,
    y: imageDisplayHeight.value / imageNaturalHeight.value
  }

  // 根据标记类型绘制不同图形
  switch (markData.type) {
    case 'line':
      if (Array.isArray(markData.data) && markData.data.length >= 2) {
        const [start, end] = markData.data
        ctx.beginPath()
        ctx.moveTo(
          imageLeft + start.x * scale.x,
          imageTop + start.y * scale.y
        )
        ctx.lineTo(
          imageLeft + end.x * scale.x,
          imageTop + end.y * scale.y
        )
        ctx.stroke()
      }
      break

    case 'circle':
      if (markData.data && typeof markData.data === 'object') {
        const { x, y, radius } = markData.data
        ctx.beginPath()
        ctx.arc(
          imageLeft + x * scale.x,
          imageTop + y * scale.y,
          radius * scale.x,
          0,
          2 * Math.PI
        )
        ctx.stroke()
      }
      break

    case 'rect':
      if (markData.data && typeof markData.data === 'object') {
        const { x, y, w, h } = markData.data
        ctx.strokeRect(
          imageLeft + x * scale.x,
          imageTop + y * scale.y,
          w * scale.x,
          h * scale.y
        )
      }
      break

    case 'polygon':
      if (Array.isArray(markData.data) && markData.data.length > 2) {
        const points = markData.data
        ctx.beginPath()
        ctx.moveTo(
          imageLeft + points[0].x * scale.x,
          imageTop + points[0].y * scale.y
        )

        for (let i = 1; i < points.length; i++) {
          ctx.lineTo(
            imageLeft + points[i].x * scale.x,
            imageTop + points[i].y * scale.y
          )
        }

        // 闭合多边形
        ctx.lineTo(
          imageLeft + points[0].x * scale.x,
          imageTop + points[0].y * scale.y
        )
        ctx.stroke()
      }
      break
  }
}

// 原始数据
const handleOriginal = () => {
  console.log('原始数据')
}

// 处理后的数据
const handleDeal = () => {
  console.log('处理后的数据')
}
// 监听src变化
watch(() => props.src, () => {
  if (props.src) {
    // 当src变化时，重置绘图状态
    clearCanvas()
    isDrawing.value = false
    currentTool.value = ''
    polygonPoints.value = []
    isDrawingPolygon.value = false
  }
})

onMounted(() => {
  if (props.src && imageEl.value) {
    // 检查图片是否已加载
    if (imageEl.value.complete) {
      handleImageLoad()
    }
  }
})

// 暴露方法给父组件
defineExpose({
  setMark
})
</script>

<style lang="less" scoped>
.insp-image-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;

  .insp-image-toolbar {
    display: flex;
    align-items: center;
    padding-bottom: @cw-gap;
    gap: @cw-gap;
    border-bottom: 1px solid @cw-border-color;

    .tool-item {
      padding: 4px 12px;
      border-radius: 4px;
      font-size: 12px;
      cursor: pointer;
      user-select: none;
      background-color: @cw-background-color;
      transition: all 0.2s;
      color: @cw-font-color;

      &:hover {
        background-color: @cw-border-color-active;
        color: #fff;
      }

      &.active {
        background-color: @cw-border-color-active;
        color: #fff;
      }
    }
  }

  .insp-image-wrapper {
    width: 100%;
    flex: 1;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    gap: @cw-gap;

    &-top {
      width: 100%;
      flex: 1;
      display: flex;
      gap: @cw-gap;
      box-sizing: border-box;

      &-left {
        width: 90%;
        height: 100%;
        box-sizing: border-box;

        .insp-image-content {
          width: 100%;
          height: 100%;
          position: relative;
          overflow: hidden;

          .image {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
          }

          .drawing-canvas {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
          }
        }
      }

      &-right {
        width: 10%;
        height: 100%;
        box-sizing: border-box;
      }
    }

    &-bottom {
      width: 90%;
      display: flex;
      flex-direction: column;
      gap: @cw-gap;

      &-top {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0 10%;

        div {
          font-size: 12px;
          color: @cw-font-color;
        }
      }

      &-bottom {
        display: flex;
        justify-content: center;
        align-items: center;
        gap: @cw-gap;
      }
    }
  }


}
</style>
