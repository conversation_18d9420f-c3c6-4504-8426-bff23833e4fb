<template>
  <div class="area-flattening-container">
    <el-row :gutter="24">
      <!-- Inputs 区域 -->
      <el-col :span="12">
        <div class="section-title">Inputs</div>
        <el-row :gutter="16" class="param-form">
          <!-- Plane Fit Method -->
          <el-col :span="24">
            <div class="param-item">
              <div class="param-label">Plane Fit Method</div>
              <div class="param-value">
                <el-select v-model="params.planeFitMethod" size="small" style="width: 100%">
                  <el-option label="X" value="x" />
                  <el-option label="Y" value="y" />
                  <el-option label="XY" value="xy" />
                </el-select>
              </div>
            </div>
          </el-col>

          <!-- Plane Fit Order -->
          <el-col :span="24">
            <div class="param-item">
              <div class="param-label">Plane Fit Order</div>
              <div class="param-value">
                <el-select v-model="params.planeFitOrder" size="small" style="width: 100%">
                  <el-option label="0th" value="0th" />
                  <el-option label="1st" value="1st" />
                  <el-option label="2nd" value="2nd" />
                  <el-option label="3rd" value="3rd" />
                  <el-option label="4th" value="4th" />
                  <el-option label="5th" value="5th" />
                  <el-option label="6th" value="6th" />
                </el-select>
              </div>
            </div>
          </el-col>

          <!-- Plane Fit Z Threshold Direction -->
          <el-col :span="24">
            <div class="param-item">
              <div class="param-label">Plane Fit Z Threshold Direction</div>
              <div class="param-value">
                <el-select v-model="params.planeFitZThresholdDirection" size="small" style="width: 100%">
                  <el-option label="Z >= " value="1" />
                  <el-option label="No Thresholding" value="2" />
                  <el-option label="Z < " value="3" />
                </el-select>
              </div>
            </div>
          </el-col>

          <!-- Plane Fit Z Threshold Percent -->
          <el-col :span="24">
            <div class="param-item">
              <div class="param-label">Plane Fit Z Threshold Percent</div>
              <div class="param-value">
                <el-input v-model="params.planeFitZThresholdPercent" size="small" />
              </div>
            </div>
          </el-col>

          <!-- Add Heigher Order -->
          <el-col :span="24">
            <div class="param-item">
              <div class="param-label">Add Heigher Order</div>
              <div class="param-value">
                <el-select v-model="params.addHeigherOrder" size="small" style="width: 100%">
                  <el-option label="On" value="1" />
                  <el-option label="Off" value="2" />
                </el-select>
              </div>
            </div>
          </el-col>


          <!-- Mark Excluded Data -->
          <el-col :span="24">
            <div class="param-item">
              <div class="param-label">Mark Excluded Data</div>
              <div class="param-value">
                <el-select v-model="params.markExcludedData" size="small" style="width: 100%">
                  <el-option label="No" value="1" />
                  <el-option label="Yes" value="2" />
                </el-select>
              </div>
            </div>
          </el-col>
        </el-row>

        <div class="export-button-container">
          <el-button type="primary" @click="handleExcute">执行</el-button>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'

const params = ref({
  planeFitMethod: 'x',
  planeFitOrder: '0th',
  planeFitZThresholdDirection: '1',
  planeFitZThresholdPercent: '0.000',
  addHeigherOrder: '1',
  markExcludedData: '1',
})

const handleExcute = () => {
  console.log('执行', params.value)
}
</script>

<style scoped lang="less">
.area-flattening-container {
  width: 100%;
  height: 100%;
  box-sizing: border-box;

  .section-title {
    font-size: 16px;
    font-weight: 600;
    color: @cw-font-color;
    text-align: center;
  }

  .param-item {
    display: flex;
    align-items: center;
    min-height: 32px;

    .param-label {
      width: 200px;
      font-size: 12px;
      color: @cw-font-color;
      text-align: right;
      padding-right: @cw-gap;
      white-space: nowrap;
    }

    .param-value {
      flex: 1;

      .el-input,
      .el-select {
        width: 100%;
      }
    }
  }

  .export-button-container {
    display: flex;
    justify-content: center;
    margin-top: 24px;
  }
}
</style>
