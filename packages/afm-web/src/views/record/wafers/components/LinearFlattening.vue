<template>
  <div class="linear-flattening-container">
    <el-row :gutter="24">
      <!-- Inputs 区域 -->
      <el-col :span="12">
        <div class="section-title">Inputs</div>
        <el-row :gutter="16" class="param-form">
          <!-- Flatten Order -->
          <el-col :span="24">
            <div class="param-item">
              <div class="param-label">Flatten Order</div>
              <div class="param-value">
                <el-select v-model="params.flattenOrder" size="small" style="width: 100%">
                  <el-option label="0th" value="0th" />
                  <el-option label="1st" value="1st" />
                  <el-option label="2nd" value="2nd" />
                  <el-option label="3rd" value="3rd" />
                  <el-option label="4th" value="4th" />
                  <el-option label="5th" value="5th" />
                  <el-option label="6th" value="6th" />

                </el-select>
              </div>
            </div>
          </el-col>

          <!-- Flatten Z Thresholding Direction -->
          <el-col :span="24">
            <div class="param-item">
              <div class="param-label">Flatten Z Thresholding Direction</div>
              <div class="param-value">
                <el-select v-model="params.flattenZThresholdingDirection" size="small" style="width: 100%">
                  <el-option label="Z >= " value="1" />
                  <el-option label="No Thresholding" value="2" />
                  <el-option label="Z < " value="3" />
                </el-select>
              </div>
            </div>
          </el-col>

          <!-- Find Threshold For -->
          <el-col :span="24">
            <div class="param-item">
              <div class="param-label">Find Threshold For</div>
              <div class="param-value">
                <el-select v-model="params.findThresholdFor" size="small" style="width: 100%">
                  <el-option label="Each Line" value="1" />
                  <el-option label="The Whole Image" value="2" />
                </el-select>
              </div>
            </div>
          </el-col>

          <!-- Mark Excluded Data -->
          <el-col :span="24">
            <div class="param-item">
              <div class="param-label">Mark Excluded Data</div>
              <div class="param-value">
                <el-select v-model="params.markExcludedData" size="small" style="width: 100%">
                  <el-option label="No" value="1" />
                  <el-option label="Yes" value="2" />
                </el-select>
              </div>
            </div>
          </el-col>

          <!-- Number Histogram Bins -->
          <el-col :span="24">
            <div class="param-item">
              <div class="param-label">Number Histogram Bins</div>
              <div class="param-value">
                <el-input v-model="params.numberHistogramBins" size="small" />
              </div>
            </div>
          </el-col>

          <!-- Threshold Height -->
          <el-col :span="24">
            <div class="param-item">
              <div class="param-label">Threshold Height</div>
              <div class="param-value">
                <el-input v-model="params.thresholdHeight" size="small" />
              </div>
            </div>
          </el-col>

          <!-- Use Histogram -->
          <el-col :span="24">
            <div class="param-item">
              <div class="param-label">Use Histogram</div>
              <div class="param-value">
                <el-select v-model="params.useHistogram" size="small" style="width: 100%">
                  <el-option label="On" value="1" />
                  <el-option label="Off" value="2" />
                </el-select>
              </div>
            </div>
          </el-col>
        </el-row>

        <div class="export-button-container">
          <el-button type="primary" @click="handleExcute">执行</el-button>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'

// 参数数据结构
const params = ref({
  flattenOrder: '0th',
  flattenZThresholdingDirection: '1',
  findThresholdFor: '1',
  markExcludedData: '1',
  numberHistogramBins: '100',
  thresholdHeight: '0.000',
  useHistogram: '1',
})

const handleExcute = () => {
  console.log('执行', params.value)
}
</script>

<style scoped lang="less">
.linear-flattening-container {
  width: 100%;
  height: 100%;
  box-sizing: border-box;

  .section-title {
    font-size: 16px;
    font-weight: 600;
    color: @cw-font-color;
    text-align: center;
  }

  .param-item {
    display: flex;
    align-items: center;
    min-height: 32px;

    .param-label {
      width: 200px;
      font-size: 12px;
      color: @cw-font-color;
      text-align: right;
      padding-right: @cw-gap;
      white-space: nowrap;
    }

    .param-value {
      flex: 1;

      .el-input,
      .el-select {
        width: 100%;
      }
    }
  }

  .export-button-container {
    display: flex;
    justify-content: center;
    margin-top: 24px;
  }
}
</style>
