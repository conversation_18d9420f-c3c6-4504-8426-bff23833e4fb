<template>
  <div class="rotate-container">
    <el-row :gutter="24">
      <!-- 左侧 Inputs 区域 -->
      <el-col :span="12">
        <div class="section-title">Inputs</div>
        <el-row :gutter="16" class="param-form">
          <!-- Rotate Angle -->
          <el-col :span="24">
            <div class="param-item">
              <div class="param-label">Rotate Angle</div>
              <div class="param-value">
                <el-input v-model="params.rotateAngle" size="small" />
              </div>
            </div>
          </el-col>
        </el-row>
      </el-col>

      <!-- 右侧 Results 区域 -->
      <el-col :span="12">
        <div class="section-title">Results</div>
        <el-row :gutter="16" class="param-form">
          <!-- Error -->
          <el-col :span="24">
            <div class="param-item">
              <div class="param-label">Error</div>
              <div class="param-value">
                <el-input v-model="results.error" size="small" readonly />
              </div>
            </div>
          </el-col>
        </el-row>
      </el-col>
    </el-row>

    <!-- 执行按钮 -->
    <div class="action-button-container">
      <el-button type="primary" @click="executeRotate">执行</el-button>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'

// 参数数据结构
const params = ref({
  rotateAngle: '40.000'
})

// 结果数据结构
const results = ref({
  error: ''
})

// 执行旋转处理函数
const executeRotate = () => {
  console.log('执行旋转', params.value)
  // 这里添加旋转执行的逻辑
  // 模拟处理结果
  results.value.error = ''
}
</script>

<style scoped lang="less">
.rotate-container {
  width: 100%;
  height: 100%;
  box-sizing: border-box;

  .section-title {
    font-size: 16px;
    font-weight: 600;
    color: @cw-font-color;
    text-align: center;
    margin-bottom: 16px;
  }

  .param-form {
    margin-top: 16px;
  }

  .param-item {
    display: flex;
    align-items: center;
    min-height: 32px;
    margin-bottom: 12px;

    .param-label {
      width: 160px;
      font-size: 12px;
      color: @cw-font-color;
      text-align: right;
      padding-right: @cw-gap;
      white-space: nowrap;
    }

    .param-value {
      flex: 1;

      .el-input {
        width: 100%;
      }
    }
  }

  .action-button-container {
    display: flex;
    justify-content: center;
    margin-top: 24px;
  }
}
</style>
