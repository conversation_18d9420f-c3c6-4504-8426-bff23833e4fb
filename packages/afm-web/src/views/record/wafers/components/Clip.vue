<template>
  <div class="clip-container">
    <el-row :gutter="24">
      <!-- 左侧 Inputs 和 Include Channels 区域 -->
      <el-col :span="12">
        <!-- Inputs 区域 -->
        <div class="section-title">Inputs</div>
        <el-row :gutter="16" class="param-form">
          <!-- Crop V5 Compatible -->
          <el-col :span="24">
            <div class="param-item">
              <div class="param-label">Crop V5 Compatible</div>
              <div class="param-value">
                <el-select v-model="params.cropV5Compatible" size="small" style="width: 100%">
                  <el-option label="Yes" value="yes" />
                  <el-option label="No" value="no" />
                </el-select>
              </div>
            </div>
          </el-col>

          <!-- Save Channels Separately -->
          <el-col :span="24">
            <div class="param-item">
              <div class="param-label">Save Channels Separately</div>
              <div class="param-value">
                <el-select v-model="params.saveChannelsSeparately" size="small" style="width: 100%">
                  <el-option label="Yes" value="yes" />
                  <el-option label="No" value="no" />
                </el-select>
              </div>
            </div>
          </el-col>

          <!-- Save Subset File -->
          <el-col :span="24">
            <div class="param-item">
              <div class="param-label">Save Subset File</div>
              <div class="param-value">
                <el-select v-model="params.saveSubsetFile" size="small" style="width: 100%">
                  <el-option label="Yes" value="yes" />
                  <el-option label="No" value="no" />
                </el-select>
              </div>
            </div>
          </el-col>
        </el-row>

        <!-- Include Channels 区域 -->
        <div class="section-title" style="margin-top: 24px;">Include Channels</div>
        <el-row :gutter="16" class="param-form">
          <!-- Channel 1 -->
          <el-col :span="24">
            <div class="param-item">
              <div class="param-label">Channel 1</div>
              <div class="param-value">
                <el-select v-model="params.channel1" size="small" style="width: 100%">
                  <el-option label="Yes" value="yes" />
                  <el-option label="No" value="no" />
                </el-select>
              </div>
            </div>
          </el-col>

          <!-- Channel 2 -->
          <el-col :span="24">
            <div class="param-item">
              <div class="param-label">Channel 2</div>
              <div class="param-value">
                <el-select v-model="params.channel2" size="small" style="width: 100%">
                  <el-option label="Yes" value="yes" />
                  <el-option label="No" value="no" />
                </el-select>
              </div>
            </div>
          </el-col>

          <!-- Channel 3 -->
          <el-col :span="24">
            <div class="param-item">
              <div class="param-label">Channel 3</div>
              <div class="param-value">
                <el-select v-model="params.channel3" size="small" style="width: 100%">
                  <el-option label="Yes" value="yes" />
                  <el-option label="No" value="no" />
                </el-select>
              </div>
            </div>
          </el-col>

          <!-- Channel 4 -->
          <el-col :span="24">
            <div class="param-item">
              <div class="param-label">Channel 4</div>
              <div class="param-value">
                <el-select v-model="params.channel4" size="small" style="width: 100%">
                  <el-option label="Yes" value="yes" />
                  <el-option label="No" value="no" />
                </el-select>
              </div>
            </div>
          </el-col>

          <!-- Channel 5 -->
          <el-col :span="24">
            <div class="param-item">
              <div class="param-label">Channel 5</div>
              <div class="param-value">
                <el-select v-model="params.channel5" size="small" style="width: 100%">
                  <el-option label="Yes" value="yes" />
                  <el-option label="No" value="no" />
                </el-select>
              </div>
            </div>
          </el-col>

          <!-- Channel 6 -->
          <el-col :span="24">
            <div class="param-item">
              <div class="param-label">Channel 6</div>
              <div class="param-value">
                <el-select v-model="params.channel6" size="small" style="width: 100%">
                  <el-option label="Yes" value="yes" />
                  <el-option label="No" value="no" />
                </el-select>
              </div>
            </div>
          </el-col>
        </el-row>

        <!-- 输出按钮 -->
        <div class="export-button-container">
          <el-button type="primary" @click="exportClip">输出</el-button>
        </div>
      </el-col>

      <!-- 右侧 Results 区域 -->
      <el-col :span="12">
        <div class="section-title">Results</div>
        <el-row :gutter="16" class="param-form">
          <!-- Number of X points -->
          <el-col :span="24">
            <div class="param-item">
              <div class="param-label">Number of X points</div>
              <div class="param-value">
                <el-input v-model="results.numberOfXPoints" size="small" readonly />
              </div>
            </div>
          </el-col>

          <!-- Number of Y Lines -->
          <el-col :span="24">
            <div class="param-item">
              <div class="param-label">Number of Y Lines</div>
              <div class="param-value">
                <el-input v-model="results.numberOfYLines" size="small" readonly />
              </div>
            </div>
          </el-col>

          <!-- Aspect Ratio -->
          <el-col :span="24">
            <div class="param-item">
              <div class="param-label">Aspect Ratio</div>
              <div class="param-value">
                <el-input v-model="results.aspectRatio" size="small" readonly />
              </div>
            </div>
          </el-col>

          <!-- Scan Size -->
          <el-col :span="24">
            <div class="param-item">
              <div class="param-label">Scan Size</div>
              <div class="param-value">
                <el-input v-model="results.scanSize" size="small" readonly />
              </div>
            </div>
          </el-col>

          <!-- Error -->
          <el-col :span="24">
            <div class="param-item">
              <div class="param-label">Error</div>
              <div class="param-value">
                <el-input v-model="results.error" size="small" readonly />
              </div>
            </div>
          </el-col>
        </el-row>
      </el-col>
    </el-row>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'

// 参数数据结构
const params = ref({
  // Inputs 区域参数
  cropV5Compatible: 'yes',
  saveChannelsSeparately: 'yes',
  saveSubsetFile: 'yes',

  // Include Channels 区域参数
  channel1: 'yes',
  channel2: 'yes',
  channel3: 'yes',
  channel4: 'yes',
  channel5: 'yes',
  channel6: 'yes'
})

// Results 区域数据
const results = ref({
  numberOfXPoints: '40.000',
  numberOfYLines: '40.000',
  aspectRatio: '2.079',
  scanSize: '0.000',
  error: '10'
})

// 输出处理函数
const exportClip = () => {
  console.log('输出Clip', params.value)
  // 这里添加输出Clip的逻辑
}
</script>

<style scoped lang="less">
.clip-container {
  width: 100%;
  height: 100%;
  box-sizing: border-box;

  .section-title {
    font-size: 16px;
    font-weight: 600;
    color: @cw-font-color;
    text-align: center;
    margin-bottom: 16px;
  }

  .param-item {
    display: flex;
    align-items: center;
    min-height: 32px;
    margin-bottom: 8px;

    .param-label {
      width: 160px;
      font-size: 12px;
      color: @cw-font-color;
      text-align: right;
      padding-right: @cw-gap;
      white-space: nowrap;
    }

    .param-value {
      flex: 1;

      .el-input,
      .el-select {
        width: 100%;
      }
    }
  }

  .export-button-container {
    display: flex;
    justify-content: center;
    margin-top: 24px;
  }
}
</style>
