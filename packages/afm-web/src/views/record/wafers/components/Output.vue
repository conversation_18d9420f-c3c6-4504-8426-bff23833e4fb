<template>
  <div class="output-container">
    <el-row :gutter="24">

      <el-col :span="12">
        <el-row :gutter="16" class="param-form">
          <!-- Exports Style -->
          <el-col :span="24">
            <div class="param-item">
              <div class="param-label">Exports Style</div>
              <div class="param-value">
                <el-select v-model="params.exportsStyle" size="small" style="width: 100%">
                  <el-option label="BMP" value="bmp" />
                  <el-option label="PNG" value="png" />
                  <el-option label="JPG" value="jpg" />
                  <el-option label="TIFF" value="tiff" />
                </el-select>
              </div>
            </div>
          </el-col>

          <!-- Dots Per Inch -->
          <el-col :span="24">
            <div class="param-item">
              <div class="param-label">Dots Per Inch</div>
              <div class="param-value">
                <el-input v-model="params.dotsPerInch" size="small" />
              </div>
            </div>
          </el-col>

          <!-- Frame Width(Inches) -->
          <el-col :span="24">
            <div class="param-item">
              <div class="param-label">Frame Width(Inches)</div>
              <div class="param-value">
                <el-input v-model="params.frameWidth" size="small" />
              </div>
            </div>
          </el-col>

          <!-- Frame Height(Inches) -->
          <el-col :span="24">
            <div class="param-item">
              <div class="param-label">Frame Height(Inches)</div>
              <div class="param-value">
                <el-input v-model="params.frameHeight" size="small" />
              </div>
            </div>
          </el-col>

          <!-- Keep Original Pixels -->
          <el-col :span="24">
            <div class="param-item">
              <div class="param-label">Keep Original Pixels</div>
              <div class="param-value">
                <el-select v-model="params.keepOriginalPixels" size="small" style="width: 100%">
                  <el-option label="No" value="no" />
                  <el-option label="Yes" value="yes" />
                </el-select>
              </div>
            </div>
          </el-col>

          <!-- Font Size -->
          <el-col :span="24">
            <div class="param-item">
              <div class="param-label">Font Size</div>
              <div class="param-value">
                <el-input v-model="params.fontSize" size="small" />
              </div>
            </div>
          </el-col>

          <!-- Font Name -->
          <el-col :span="24">
            <div class="param-item">
              <div class="param-label">Font Name</div>
              <div class="param-value">
                <el-input v-model="params.fontName" size="small" />
              </div>
            </div>
          </el-col>

          <!-- Show Color Bar -->
          <el-col :span="24">
            <div class="param-item">
              <div class="param-label">Show Color Bar</div>
              <div class="param-value">
                <el-select v-model="params.showColorBar" size="small" style="width: 100%">
                  <el-option label="Off Image" value="off" />
                  <el-option label="On Image" value="on" />
                </el-select>
              </div>
            </div>
          </el-col>

          <!-- Show Data Scale -->
          <el-col :span="24">
            <div class="param-item">
              <div class="param-label">Show Data Scale</div>
              <div class="param-value">
                <el-select v-model="params.showDataScale" size="small" style="width: 100%">
                  <el-option label="No" value="no" />
                  <el-option label="Yes" value="yes" />
                </el-select>
              </div>
            </div>
          </el-col>

          <!-- Show Scan Size Bar -->
          <el-col :span="24">
            <div class="param-item">
              <div class="param-label">Show Scan Size Bar</div>
              <div class="param-value">
                <el-select v-model="params.showScanSizeBar" size="small" style="width: 100%">
                  <el-option label="Off Image" value="off" />
                  <el-option label="On Image" value="on" />
                </el-select>
              </div>
            </div>
          </el-col>

          <!-- Show Scan Size Text -->
          <el-col :span="24">
            <div class="param-item">
              <div class="param-label">Show Scan Size Text</div>
              <div class="param-value">
                <el-select v-model="params.showScanSizeText" size="small" style="width: 100%">
                  <el-option label="Off Image" value="off" />
                  <el-option label="On Image" value="on" />
                </el-select>
              </div>
            </div>
          </el-col>

          <!-- Show Data Type -->
          <el-col :span="24">
            <div class="param-item">
              <div class="param-label">Show Data Type</div>
              <div class="param-value">
                <el-select v-model="params.showDataType" size="small" style="width: 100%">
                  <el-option label="Off Image" value="off" />
                  <el-option label="On Image" value="on" />
                </el-select>
              </div>
            </div>
          </el-col>

          <!-- Show Annotation -->
          <el-col :span="24">
            <div class="param-item">
              <div class="param-label">Show Annotation</div>
              <div class="param-value">
                <el-select v-model="params.showAnnotation" size="small" style="width: 100%">
                  <el-option label="Off Image" value="off" />
                  <el-option label="On Image" value="on" />
                </el-select>
              </div>
            </div>
          </el-col>
        </el-row>

      </el-col>
    </el-row>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'

const params = ref({
  exportsStyle: 'bmp',
  dotsPerInch: '2000',
  frameWidth: '4.40',
  frameHeight: '3.60',
  keepOriginalPixels: 'no',
  fontSize: '14',
  fontName: '',
  showColorBar: 'off',
  showDataScale: 'no',
  showScanSizeBar: 'off',
  showScanSizeText: 'off',
  showDataType: 'off',
  showAnnotation: 'off',
})
</script>

<style scoped lang="less">
.output-container {
  width: 100%;
  height: 100%;
  box-sizing: border-box;

  .param-item {
    display: flex;
    align-items: center;
    min-height: 32px;

    .param-label {
      width: 160px;
      font-size: 12px;
      color: @cw-font-color;
      text-align: right;
      padding-right: @cw-gap;
      white-space: nowrap;
    }

    .param-value {
      flex: 1;

      .el-input,
      .el-select {
        width: 100%;
      }
    }
  }
}
</style>
