<template>
  <div class="perf-image" ref="container" @contextmenu.prevent>
    <!-- 状态栏 -->
    <div class="status-bar">
      <div class="status-item" v-for="status in statuses" :key="status.type" :title="status.label"
        :class="{ active: screenMode === status.type }" @click="handleStatusClick(status)">
        <i class="iconfont" :class="status.icon"></i>
      </div>
    </div>
    <!-- 瓦片模式绘图工具栏 -->
    <div class="tool-bar" v-if="screenMode === 'tiles'">
      <div class="tool-item" v-for="tool in tileTools" :key="tool.type" :title="tool.label"
        :class="{ active: currentTileTool === tool.type }" @click="handleTileToolClick(tool)">
        <i class="iconfont" :class="tool.icon"></i>
      </div>
    </div>
    <!-- 实时模式绘图工具栏 -->
    <div class="tool-bar" v-if="screenMode === 'real-time'">
      <div class="tool-item" v-for="tool in realTimeTools" :key="tool.type" :title="tool.label"
        :class="{ active: currentRealTimeTool === tool.type }" @click="handleRealTimeToolClick(tool)">
        <i class="iconfont" :class="tool.icon"></i>
      </div>
    </div>
    <!-- Map 模式绘图工具栏 -->
    <div class="tool-bar" v-if="screenMode === 'video-off'">
      <div class="tool-item" v-for="tool in mapTools" :key="tool.type" :title="tool.label"
        :class="{ active: currentMapTool === tool.type }" @click="handleMapToolClick(tool)">
        <i class="iconfont" :class="tool.icon"></i>
      </div>
    </div>
    <!-- 实时加载器 -->
    <div ref="imageContainer" class="tiles-container" v-if="screenMode === 'real-time'" @wheel="handleImageWheel"
      @contextmenu.prevent>
      <div class="content-wrapper">
        <canvas ref="imageCanvas" class="image-canvas" @click="handleImageCanvasClick"
          @mousedown="handleImageMouseDown"></canvas>
        <canvas ref="imageDisplayCanvas" class="display-canvas"></canvas>
      </div>
    </div>
    <!-- 内触发加载器 -->
    <div ref="innerVideoContainer" class="tiles-container" v-else-if="screenMode === 'inner-video'">
      <div class="content-wrapper">
        <canvas ref="innerVideoCanvas" class="display-canvas"></canvas>
      </div>
    </div>
    <!-- Map 加载器 -->
    <div ref="mapContainer" class="tiles-container" v-else-if="screenMode === 'video-off'">
      <div class="map-container" @wheel="handleMapWheel" @mousedown="handleMapDragStart">
        <div class="map-content">
          <canvas ref="mapLayerCanvas" class="map-layer"></canvas>
          <canvas ref="mapDrawCanvas" class="map-draw-layer"></canvas>
        </div>
      </div>
      <!-- Map单元格坐标提示 -->
      <div v-if="showMapTooltip" class="map-tooltip" :style="mapTooltipStyle">
        {{ mapTooltipText }}
      </div>
    </div>
    <!-- 瓦片加载器 -->
    <div ref="stageContainer" class="tiles-container" v-else-if="screenMode === 'tiles'" @wheel="handleWheel"
      @mousedown="handleDragStart" @contextmenu.prevent></div>
    <!-- 加载状态提示 -->
    <div v-if="!props.tilesReady && screenMode === 'tiles'" class="loading-indicator">
      等待数据准备中...
    </div>
    <!-- 长度指示器 -->
    <div v-if="showLength" class="length-indicator" :style="lengthIndicatorStyle">
      {{ currentLength }}px
    </div>
    <!-- 坐标指示器 -->
    <div v-if="showCoordinates && (screenMode === 'tiles') && grayValue !== -1" class="coordinates-indicator">
      <div>X: {{ coordinates.x }}, Y: {{ coordinates.y }}, GS: {{ grayValue }}</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, onBeforeUnmount, nextTick, computed } from 'vue'
import { ImageConfig, Mark, ScreenMode, MapDataType } from './type'
import { useStatusBar, useTilesMode, useRealTimeMode, useInnerVideoMode, useMapMode } from './composable'
import { updateWebGLTexture } from './tools'
import { drawConfig } from './config'

const props = defineProps<{
  imageUrl: string // 实时图片 URL
  initialMarks?: Mark[] // 初始瓦片标记
  tilesReady?: boolean // 瓦片是否准备好
  imageConfig: ImageConfig // 瓦片图片配置
  tilesUrl: string // 瓦片图片 URL
  mapData?: MapDataType // Map数据
  mapRatio: number // Map 单元格比例（宽：高）
  isTile: boolean // 是否是瓦片模式
}>()

const container = ref<HTMLElement | null>(null) // 容器
const useWebGL = ref(true) // 实时模式和内触发模式是否使用WebGL渲染
const tilesUrl = ref(props.tilesUrl)
const imageConfig = ref(props.imageConfig)
const isReady = computed(() => props.tilesReady)

const screenMode = defineModel<ScreenMode>('screenMode', { required: true })

const emit = defineEmits(['send', 'move', 'videoOn', 'videoOff', 'photo', 'post'])

const { statuses, handleStatusClick } = useStatusBar(screenMode) // 状态栏相关

const {
  currentTileTool,
  tileTools,
  handleTileToolClick,
  tileCache,
  polygonPoints,
  currentPolygon,
  stageContainer,
  stage,
  layer,
  drawLayer,
  marks,
  scale,
  position,
  simplifiedMode,
  showLength,
  currentLength,
  lengthIndicatorStyle,
  showCoordinates,
  coordinates,
  grayValue,
  isDrawMode,
  isDragging,
  initStage,
  renderMarks,
  handleWheel,
  handleWheelEnd,
  handleDragStart,
  handleDragMove,
  handleDragEnd,
  handlePolygonDrawMove,
  handleKeyDown,
  resetFullImage,
} = useTilesMode({
  ...props,
  initialMarks: props.initialMarks || []
}, screenMode, container, isReady, tilesUrl, imageConfig) // 瓦片模式相关

const {
  axisRef,
  imageContainer,
  imageCanvas,
  imageDisplayCanvas,
  imageScale,
  cachedAxis,
  imagePool,
  lastImageUrl,
  realTimeGLRenderer,
  resizeObserver,
  imageProcessQueue,
  isProcessingQueue,
  showAxis,
  currentRealTimeTool,
  realTimeTools,
  handleRealTimeToolClick,
  initImageCanvas,
  queueImageData,
  handleImageWheel,
  handleImageMouseDown,
  handleImageCanvasClick,
  cleanupImageRendering,
} = useRealTimeMode(props, screenMode, emit, container, useWebGL) // 实时模式相关

const {
  innerVideoImagePool,
  innerVideoContainer,
  innerVideoCanvas,
  lastInnerVideoUrl,
  innerVideoGLRenderer,
  innerVideoResizeObserver,
  innerVideoQueue,
  isProcessingInnerVideoQueue,
  initInnerVideo,
  queueInnerVideoData,
  cleanupInnerVideoRendering,
} = useInnerVideoMode(props, screenMode, useWebGL) // 内触发模式相关

const {
  mapContainer,
  mapLayerCanvas,
  mapDrawCanvas,
  mapLayerContext,
  mapDrawContext,
  mapScale,
  mapPosition,
  mapDragging,
  mapIsDrawing,
  mapSelectionStart,
  mapSelectionCurrent,
  maps,
  mapWorker,
  showMapTooltip,
  mapTooltipText,
  mapTooltipStyle,
  mapHoverTimer,
  mapMarkPool,
  mapDataManager,
  loadedMapIndexes,
  currentMapTool,
  mapTools,
  handleMapToolClick,
  initMap,
  clearMapLayerCache,
  handleMapClick,
  handleMapWheel,
  handleMapDragStart,
  handleMapMouseMove,
  handleMapMouseUp,
  clearMapTooltip,
  loadVisibleMapMarks,
} = useMapMode(props, screenMode, emit, container) // Map模式相关

// 模式变化，重新渲染
watch(() => screenMode.value, (newMode, oldMode) => {
  // 先清理旧模式的资源
  if (oldMode) {
    cleanupMode(oldMode)
  }

  if (newMode === 'tiles') {
    nextTick(() => {
      initStage()
    })
  }
  if (newMode === 'real-time') {
    emit('videoOn', 1)
    nextTick(() => {
      initImageCanvas()
    })
  }
  if (newMode === 'video-off') {
    emit('videoOff')
    nextTick(() => {
      initMap()
    })
  }
  if (newMode === 'inner-video') {
    emit('videoOn', 2)
    nextTick(() => {
      initInnerVideo()
    })
  }
  if (newMode === 'image') {
    emit('photo')
  }
})

// 实时模式和内触发模式：图像URL变化，重新渲染
watch(() => props.imageUrl, (newUrl, oldUrl) => {
  if (!newUrl) return

  if (screenMode.value === 'real-time' && newUrl !== lastImageUrl.value) {
    lastImageUrl.value = newUrl

    // WebGL模式使用节流控制
    if (useWebGL.value && realTimeGLRenderer.value) {
      updateWebGLTexture(realTimeGLRenderer.value, newUrl)
    } else {
      // Canvas模式使用队列控制
      queueImageData(newUrl)
    }
  } else if (screenMode.value === 'inner-video' && newUrl !== lastInnerVideoUrl.value) {
    lastInnerVideoUrl.value = newUrl

    // WebGL模式使用节流控制
    if (useWebGL.value && innerVideoGLRenderer.value) {
      updateWebGLTexture(innerVideoGLRenderer.value, newUrl)
    } else {
      // Canvas模式使用队列控制
      queueInnerVideoData(newUrl)
    }
  }
}, { flush: 'post' })

// Map 模式：数据变化，重新渲染
watch(() => props.mapData, (newData) => {
  if (newData && screenMode.value === 'video-off') {
    nextTick(() => {
      initMap()
    })
  }
}, { deep: true })

// 瓦片模式：瓦片URL变化，重新渲染
watch(() => props.tilesUrl, (newUrl) => {
  if (newUrl) {
    tileCache.clear()
    tilesUrl.value = newUrl // 触发visiletiles重新计算

    // 不切图模式下，需要重置fullImage并重新初始化
    if (!props.isTile && screenMode.value === 'tiles') {
      resetFullImage()
      nextTick(() => {
        initStage()
      })
    }
  }
})

// 瓦片模式：瓦片ImageConfig变化，重新渲染
watch(() => props.imageConfig, (newImageConfig) => {
  tileCache.clear()
  imageConfig.value = newImageConfig

  // 如果当前是瓦片模式，更新图片位置
  if (screenMode.value === 'tiles' && stage.value && stageContainer.value) {
    // 计算初始位置,使图像位于视口中心
    const containerWidth = stageContainer.value.clientWidth
    const containerHeight = stageContainer.value.clientHeight
    const currentLevelScale = 1 / Math.pow(newImageConfig.factor, newImageConfig.maxZoom - 1)
    const initialWidth = newImageConfig.width * currentLevelScale
    const initialHeight = newImageConfig.height * currentLevelScale

    // 更新位置，使图片居中
    position.value = {
      x: (containerWidth - initialWidth) / 2,
      y: (containerHeight - initialHeight) / 2
    }

    // 更新舞台位置
    stage.value.position(position.value)
    stage.value.batchDraw()
  }
}, { deep: true })

// 监听 marks 实时更新数据
watch(marks, (newMarks) => {
  emit('send', newMarks)
}, { deep: true })

// 监听 maps 实时更新数据
watch(maps, (newMaps) => {
  emit('post', newMaps)
}, { deep: true })

// 清理特定模式的资源
const cleanupMode = (mode: ScreenMode) => {
  if (mode === 'tiles') {
    // 清理瓦片模式资源
    if (drawLayer.value) {
      drawLayer.value.destroy()
      drawLayer.value = null
    }

    if (layer.value) {
      layer.value.destroy()
      layer.value = null
    }

    if (stage.value) {
      stage.value.off('click')
      stage.value.off('mousemove')
      stage.value.off('touchmove')
      stage.value.destroy()
      stage.value = null
    }

    // 重置状态
    scale.value = 1
    position.value = { x: 0, y: 0 }
    isDragging.value = false
    isDrawMode.value = false

    // 清理多边形绘制状态
    if (currentPolygon.value) {
      currentPolygon.value = null
    }
    polygonPoints.value = []

    // 清理瓦片缓存
    tileCache.clear()

    // 清理指示器
    showLength.value = false
    showCoordinates.value = false
  } else if (mode === 'real-time') {
    // 清理实时模式资源
    if (axisRef.value) {
      axisRef.value.destroy()
      axisRef.value = null
    }

    // 清理Canvas渲染资源
    cleanupImageRendering()

    // 清理轴线缓存
    cachedAxis.clear()

    // 清理ResizeObserver
    if (resizeObserver.value) {
      resizeObserver.value.disconnect()
      resizeObserver.value = null
    }

    // 清理图像对象池
    imagePool.clear()
    innerVideoImagePool.clear()

    // 重置缩放
    imageScale.value = 1

    // 重置工具选择
    currentRealTimeTool.value = null
    showAxis.value = false
  } else if (mode === 'video-off') {
    // 清理Map模式资源
    if (mapDrawCanvas.value) {
      mapDrawCanvas.value.removeEventListener('click', handleMapClick)
      mapDrawCanvas.value.removeEventListener('mousemove', handleMapMouseMove)
    }

    // 确保移除文档级别的事件监听器
    document.removeEventListener('mouseup', handleMapMouseUp)

    // 清理定时器
    if (mapHoverTimer.value !== null) {
      window.clearTimeout(mapHoverTimer.value)
      mapHoverTimer.value = null
    }

    // 重置上下文缓存
    mapLayerContext.value = null
    mapDrawContext.value = null

    // 清除地图缓存
    clearMapLayerCache()

    // 重置状态
    mapDragging.value = false
    mapIsDrawing.value = false
    mapSelectionStart.value = null
    mapSelectionCurrent.value = null
    mapScale.value = 1
    mapPosition.value = { x: 0, y: 0 }
    currentMapTool.value = null

    // 清理Map tooltip
    clearMapTooltip()

    // 清理标记对象池
    mapMarkPool.clear()

    // 尝试触发GC
    setTimeout(() => {
      const largeObj = new Array(10000).fill(0)
      largeObj.length = 0
    }, 100)
  } else if (mode === 'inner-video') {
    // 清理内触发模式资源
    cleanupInnerVideoRendering()
  }
}

onMounted(() => {
  if (screenMode.value === 'tiles') {
    initStage()
  } else if (screenMode.value === 'real-time') {
    initImageCanvas()
  } else if (screenMode.value === 'video-off') {
    initMap()
  } else if (screenMode.value === 'inner-video') {
    initInnerVideo()
  }

  window.addEventListener('keydown', handleKeyDown)
  document.addEventListener('mousemove', handlePolygonDrawMove)
})

onBeforeUnmount(() => {
  // 移除所有全局事件监听器，无论当前模式
  window.removeEventListener('keydown', handleKeyDown)
  document.removeEventListener('mousemove', handlePolygonDrawMove)
  document.removeEventListener('mouseup', handleDragEnd)
  document.removeEventListener('mouseup', handleMapMouseUp)

  // 清理节流函数
  handleWheel.cancel?.()
  handleDragMove.cancel?.()
  handlePolygonDrawMove.cancel?.()
  handleImageWheel.cancel?.()
  handleMapWheel.cancel?.()
  handleMapMouseMove.cancel?.()
  handleWheelEnd.cancel?.()
  if (updateWebGLTexture.cancel) updateWebGLTexture.cancel()

  // 清理Canvas渲染资源
  cleanupImageRendering()
  cleanupInnerVideoRendering()

  // 清理当前模式的资源
  cleanupMode(screenMode.value)

  // 清理图像对象池
  imagePool.clear()
  innerVideoImagePool.clear()

  // 清理图像队列
  imageProcessQueue.value = []
  innerVideoQueue.value = []
  isProcessingQueue.value = false
  isProcessingInnerVideoQueue.value = false

  // 清理轴线缓存
  cachedAxis.clear()

  // 清理ResizeObserver
  if (resizeObserver.value) {
    resizeObserver.value.disconnect()
    resizeObserver.value = null
  }

  // 确保清理内触发模式的ResizeObserver
  if (innerVideoResizeObserver.value) {
    innerVideoResizeObserver.value.disconnect()
    innerVideoResizeObserver.value = null
  }

  // 销毁Map模式事件监听器
  if (mapDrawCanvas.value) {
    mapDrawCanvas.value.removeEventListener('click', handleMapClick)
    mapDrawCanvas.value.removeEventListener('mousemove', handleMapMouseMove)
  }
  document.removeEventListener('mouseup', handleMapMouseUp)

  // 取消所有节流函数
  if (handleMapWheel.cancel) handleMapWheel.cancel()
  if (handleMapMouseMove.cancel) handleMapMouseMove.cancel()

  // 销毁Map Worker资源
  mapWorker.destroy()

  // 强制清理图像资源
  forceClearImageResources()
})

// 强制 实时模式 内触发模式 图像资源
const forceClearImageResources = () => {
  // 清理队列
  imageProcessQueue.value = []
  innerVideoQueue.value = []
  isProcessingQueue.value = false
  isProcessingInnerVideoQueue.value = false

  // 清理对象池和缓存
  imagePool.clear()
  innerVideoImagePool.clear()
  cachedAxis.clear()

  // 清理 WebGL 纹理缓存
  if (realTimeGLRenderer.value) {
    realTimeGLRenderer.value.clearTextureCache?.()
  }
  if (innerVideoGLRenderer.value) {
    innerVideoGLRenderer.value.clearTextureCache?.()
  }
}

// 更新 Marks
const setMarks = (newMarks: Mark[]) => {
  // 处理每个mark的颜色，确保它们拥有颜色属性
  marks.value = newMarks.map(mark => {
    // 需要强制转换类型，添加颜色但保持原有属性
    const updatedMark = { ...mark }

    // 只有在没有自定义颜色时添加默认颜色
    if (!('strokeColor' in mark)) {
      (updatedMark as any).strokeColor = drawConfig.stroke
    }

    if (!('fillColor' in mark) && mark.type !== 'line') {
      (updatedMark as any).fillColor = drawConfig.fill
    }

    return updatedMark
  })

  // 如果标记数量超过阈值，采用延迟渲染策略
  const isLargeMarkset = marks.value.length > 200

  // 如果是瓦片模式，需要优化渲染过程
  if (screenMode.value === 'tiles' && drawLayer.value) {
    // 强制确保drawLayer可见
    drawLayer.value.visible(true)

    if (isLargeMarkset) {
      // 大数据集策略：先不渲染，等用户操作后再渲染
      // 先清空之前的所有标记
      drawLayer.value.find('.mark').forEach(mark => mark.destroy())
      drawLayer.value.find('Transformer').forEach(tr => tr.destroy())

      // 只有当用户没有拖拽时才立即渲染标记 (移除simplifiedMode条件)
      if (!isDragging.value) {
        // 分批次渲染标记以避免界面冻结
        setTimeout(() => {
          // 如果处于简化模式，先退出简化模式
          if (simplifiedMode.value) {
            simplifiedMode.value = false
          }
          renderMarks()
        }, 100)
      } else {
        // 如果正在拖拽，等拖拽结束后渲染
        setTimeout(() => {
          if (!isDragging.value) {
            if (simplifiedMode.value) {
              simplifiedMode.value = false
            }
            renderMarks()
          }
        }, 300)
      }
    } else {
      // 小数据集策略：先用简化模式快速渲染
      const wasSimplified = simplifiedMode.value
      simplifiedMode.value = true
      renderMarks()

      // 短暂延迟后渲染完整标记
      setTimeout(() => {
        simplifiedMode.value = false
        renderMarks()
      }, wasSimplified ? 150 : 100) // 如果已经是简化模式，给更多时间
    }
  }
}

// 更新 Maps
const setMaps = (newMaps: { row: number, col: number, fillColor: string, strokeColor: string }[]) => {
  if (!newMaps?.length) {
    maps.value = []
    return
  }

  console.time('地图标记处理总时间')
  // 1. 清空已有数据
  maps.value = []
  loadedMapIndexes.clear()

  // 2. 导入数据到数据管理器
  mapDataManager.setFullData(newMaps)
  mapDataManager.setProcessingState(true)

  // 3. 创建空间索引
  mapDataManager.createSpatialIndex(newMaps).then(() => {
    // 4. 初始加载可视区域标记
    loadVisibleMapMarks()
  })
}

defineExpose({
  setMarks,
  setMaps
})
</script>

<style scoped lang="less">
.perf-image {
  width: 100%;
  height: 100%;
  background-color: @cw-card-color;
  position: relative;
  user-select: none;
  touch-action: none;
  overflow: hidden;

  .status-bar {
    position: absolute;
    left: 50%;
    top: 0;
    transform: translateX(-50%);
    z-index: 1000;
    background-color: rgba(0, 0, 0, 0.7);
    border-radius: 4px;
    padding: 6px;
    display: flex;
    gap: 4px;

    .status-item {
      width: 28px;
      height: 28px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      border-radius: 3px;
      transition: all 0.3s;
      color: #fff;

      &:hover {
        background-color: rgba(255, 255, 255, 0.1);
      }

      &.active {
        background-color: rgba(255, 255, 255, 0.2);
        color: #1890ff;
      }

      i {
        font-size: 16px;
      }
    }
  }

  .tool-bar {
    position: absolute;
    top: 50%;
    right: 0;
    z-index: 1000;
    background-color: rgba(0, 0, 0, 0.7);
    border-radius: 4px;
    padding: 6px;
    display: flex;
    flex-direction: column;
    gap: 4px;
    transform: translateY(-50%);

    .tool-item {
      width: 28px;
      height: 28px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      border-radius: 3px;
      transition: all 0.3s;
      color: white;

      &:hover {
        background-color: rgba(255, 255, 255, 0.1);
      }

      &.active {
        background-color: rgba(255, 255, 255, 0.2);
        color: #1890ff;
      }

      i {
        font-size: 16px;
      }
    }
  }

  .tiles-container {
    width: 100%;
    height: 100%;
    position: relative;
    overflow: hidden;

    .content-wrapper {
      width: 100%;
      height: 100%;
      position: absolute;
      top: 0;
      left: 0;
      transform-origin: center center;
      will-change: transform;
      backface-visibility: hidden;
      -webkit-backface-visibility: hidden;
      image-rendering: pixelated;
    }

    .image-canvas {
      width: 100%;
      height: 100%;
      position: absolute;
      top: 0;
      left: 0;
      z-index: 100;
    }

    .display-canvas {
      width: 100%;
      height: 100%;
      position: absolute;
      top: 0;
      left: 0;
      z-index: 10;
    }

    img {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      z-index: 10;
    }

    .map-container {
      width: 100%;
      height: 100%;
      position: relative;
      overflow: hidden;

      .map-content {
        position: absolute;
        transform-origin: 0 0;
        will-change: transform;
        backface-visibility: hidden;
        -webkit-backface-visibility: hidden;
      }

      .map-layer {
        position: absolute;
        top: 0;
        left: 0;
        z-index: 10;
        user-select: none;
        pointer-events: none;
      }

      .map-draw-layer {
        position: absolute;
        top: 0;
        left: 0;
        z-index: 20;
        user-select: none;
        pointer-events: all;
      }
    }
  }

  .length-indicator {
    position: absolute;
    background-color: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 12px;
    transform: translate(-50%, -50%);
    pointer-events: none;
    z-index: 1000;
  }

  .coordinates-indicator {
    position: absolute;
    bottom: 0;
    left: 0;
    background-color: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 12px;
    z-index: 1000;
    pointer-events: none;
  }

  .loading-indicator {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 10px 20px;
    border-radius: 4px;
    font-size: 14px;
    z-index: 1000;
  }

  .map-tooltip {
    position: fixed;
    background-color: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    pointer-events: none;
    z-index: 1050;
    white-space: nowrap;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    transform: none;
    /* 移除水平居中变换 */
    transition: left 0.05s linear, top 0.05s linear;
    will-change: left, top;

    /* 添加向下指向的箭头 */
    &::after {
      content: '';
      position: absolute;
      top: 100%;
      left: 50%;
      margin-left: -5px;
      border-width: 5px;
      border-style: solid;
      border-color: rgba(0, 0, 0, 0.8) transparent transparent transparent;
    }
  }
}
</style>
